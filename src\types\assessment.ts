export interface AssessmentQuestion {
  id: string;
  text: string;
  category: string;
}

export interface AssessmentResponse {
  questionId: string;
  value: number; // 1-4 scale
}

export interface AssessmentCategory {
  id: string;
  name: string;
  questions: AssessmentQuestion[];
}

export interface Assessment {
  id?: string;
  sessionId: string;
  orgSize: string;
  industry: string;
  responses: Record<string, number>;
  score?: number;
  maturityLevel?: string;
  createdAt?: string;
}

export interface Lead {
  id?: string;
  assessmentId: string;
  name?: string;
  orgName?: string;
  email?: string;
  createdAt?: string;
}

export const ORG_SIZES = [
  'Micro (1-9 employees)',
  'Small (10-49 employees)',
  'Medium (50-249 employees)',
  'Large (250-999 employees)',
  'Enterprise (1000+ employees)'
];

export const INDUSTRIES = [
  'Healthcare',
  'Manufacturing',
  'Information Technology',
  'Transport & Logistics',
  'Construction',
  'Financial Services',
  'Retail & E-commerce',
  'Education',
  'Professional Services',
  'Energy & Utilities',
  'Food & Beverage',
  'Other'
];

export const ASSESSMENT_CATEGORIES: AssessmentCategory[] = [
  {
    id: 'carbon-management',
    name: 'Carbon Management',
    questions: [
      { id: 'cm1', text: 'Do you measure Scope 1, 2, and 3 emissions?', category: 'carbon-management' },
      { id: 'cm2', text: 'Do you set carbon reduction targets aligned with Net Zero?', category: 'carbon-management' },
      { id: 'cm3', text: 'Do you use carbon offsetting as part of your strategy?', category: 'carbon-management' }
    ]
  },
  {
    id: 'energy-resources',
    name: 'Energy & Resources',
    questions: [
      { id: 'er1', text: 'Do you track energy consumption across all sites?', category: 'energy-resources' },
      { id: 'er2', text: 'Do you use renewable energy sources?', category: 'energy-resources' },
      { id: 'er3', text: 'Do you have a resource efficiency plan in place?', category: 'energy-resources' }
    ]
  },
  {
    id: 'supply-chain',
    name: 'Supply Chain & Procurement',
    questions: [
      { id: 'sc1', text: 'Do you assess suppliers for sustainability performance?', category: 'supply-chain' },
      { id: 'sc2', text: 'Do you include Net Zero clauses in procurement contracts?', category: 'supply-chain' },
      { id: 'sc3', text: 'Do you collaborate with suppliers on emissions reduction?', category: 'supply-chain' }
    ]
  },
  {
    id: 'waste-circular',
    name: 'Waste & Circular Economy',
    questions: [
      { id: 'wc1', text: 'Do you measure and track waste generated?', category: 'waste-circular' },
      { id: 'wc2', text: 'Do you have a recycling and reuse strategy?', category: 'waste-circular' },
      { id: 'wc3', text: 'Do you engage with circular economy initiatives?', category: 'waste-circular' }
    ]
  },
  {
    id: 'travel-logistics',
    name: 'Travel & Logistics',
    questions: [
      { id: 'tl1', text: 'Do you monitor and report business travel emissions?', category: 'travel-logistics' },
      { id: 'tl2', text: 'Do you use sustainable logistics or EV fleets?', category: 'travel-logistics' },
      { id: 'tl3', text: 'Do you encourage remote/hybrid work policies?', category: 'travel-logistics' }
    ]
  },
  {
    id: 'governance',
    name: 'Governance & Leadership',
    questions: [
      { id: 'gl1', text: 'Do you have executive accountability for sustainability?', category: 'governance' },
      { id: 'gl2', text: 'Do you report sustainability metrics publicly?', category: 'governance' },
      { id: 'gl3', text: 'Do you provide employee training on Net Zero?', category: 'governance' }
    ]
  },
  {
    id: 'adaptation',
    name: 'Adaptation & Resilience',
    questions: [
      { id: 'ar1', text: 'Do you assess climate risks to your operations?', category: 'adaptation' },
      { id: 'ar2', text: 'Do you have adaptation plans in place?', category: 'adaptation' },
      { id: 'ar3', text: 'Do you test business continuity with climate scenarios?', category: 'adaptation' }
    ]
  }
];

export const ANSWER_OPTIONS = [
  { value: 1, label: 'Not at all', description: 'We have not started' },
  { value: 2, label: 'Basic', description: 'We have basic measures' },
  { value: 3, label: 'Good', description: 'We have good practices' },
  { value: 4, label: 'Excellent', description: 'We are leading practice' }
];

export function calculateScore(responses: Record<string, number>): { score: number; maturityLevel: string } {
  const totalQuestions = ASSESSMENT_CATEGORIES.reduce((acc, cat) => acc + cat.questions.length, 0);
  const totalScore = Object.values(responses).reduce((acc, val) => acc + val, 0);
  const maxScore = totalQuestions * 4;
  
  const percentage = Math.round((totalScore / maxScore) * 100);
  
  let maturityLevel = 'Early Stage';
  if (percentage >= 76) maturityLevel = 'Leading';
  else if (percentage >= 51) maturityLevel = 'Established';
  else if (percentage >= 25) maturityLevel = 'Developing';
  
  return { score: percentage, maturityLevel };
}

export function getCategoryScore(categoryId: string, responses: Record<string, number>): number {
  const category = ASSESSMENT_CATEGORIES.find(cat => cat.id === categoryId);
  if (!category) return 0;
  
  const categoryResponses = category.questions
    .map(q => responses[q.id] || 0)
    .filter(Boolean);
  
  if (categoryResponses.length === 0) return 0;
  
  const totalScore = categoryResponses.reduce((acc, val) => acc + val, 0);
  const maxScore = category.questions.length * 4;
  
  return Math.round((totalScore / maxScore) * 100);
}