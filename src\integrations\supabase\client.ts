// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://etbusvbldticpxvoqnya.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV0YnVzdmJsZHRpY3B4dm9xbnlhIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY5MTAwNTgsImV4cCI6MjA3MjQ4NjA1OH0.exHvMLhGaYM8NRIq36uTfMTdJ7CDTRayxPHYO8cXhEc";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});