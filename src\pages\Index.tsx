import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON> } from "react-router-dom";
import { Leaf, Target, <PERSON><PERSON><PERSON>, Award, ArrowRight, Shield, Users, Zap } from "lucide-react";

const Index = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-secondary to-background">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Leaf className="h-8 w-8 text-primary mr-3" />
              <span className="text-2xl font-bold">Net Zero Assessment</span>
            </div>
            <nav className="hidden md:flex items-center space-x-6">
              <a href="#features" className="text-muted-foreground hover:text-foreground">Features</a>
              <a href="#about" className="text-muted-foreground hover:text-foreground">About</a>
              <Link to="/assessment">
                <Button>Start Assessment</Button>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <Badge className="mb-6" variant="secondary">Free • Comprehensive • Instant Results</Badge>
          <h1 className="text-5xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            Assess Your Net Zero Readiness
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-12">
            Discover your organization's sustainability maturity across 7 key areas. 
            Get instant insights, personalized recommendations, and a clear roadmap to Net Zero.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/assessment">
              <Button size="lg" className="text-lg px-8 py-6">
                Start Free Assessment
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="text-lg px-8 py-6">
              Learn More
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-card/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4">Comprehensive Sustainability Assessment</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Evaluate your progress across all critical sustainability dimensions
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <Card className="text-center">
              <CardHeader>
                <Target className="h-12 w-12 text-primary mx-auto mb-4" />
                <CardTitle>7 Key Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Carbon Management, Energy, Supply Chain, Waste, Travel, Governance & Resilience
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <BarChart className="h-12 w-12 text-primary mx-auto mb-4" />
                <CardTitle>Instant Scoring</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Get your maturity level immediately with detailed breakdowns and radar charts
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Award className="h-12 w-12 text-primary mx-auto mb-4" />
                <CardTitle>Smart Recommendations</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Receive prioritized actions based on your lowest-scoring areas
                </CardDescription>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardHeader>
                <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
                <CardTitle>Privacy First</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Anonymous assessment with optional contact details for personalized support
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary mb-2">5 min</div>
              <div className="text-muted-foreground">Average completion time</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary mb-2">21</div>
              <div className="text-muted-foreground">Assessment questions</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary mb-2">100%</div>
              <div className="text-muted-foreground">Free forever</div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Start Your Net Zero Journey?</h2>
          <p className="text-xl mb-8 opacity-90 max-w-2xl mx-auto">
            Join organizations worldwide in building a sustainable future. 
            Take the first step with our comprehensive assessment.
          </p>
          <Link to="/assessment">
            <Button size="lg" variant="secondary" className="text-lg px-8 py-6">
              Begin Assessment Now
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 border-t bg-card/50">
        <div className="container mx-auto px-4 text-center">
          <div className="flex items-center justify-center mb-4">
            <Leaf className="h-6 w-6 text-primary mr-2" />
            <span className="font-semibold">Net Zero Assessment</span>
          </div>
          <p className="text-sm text-muted-foreground">
            Empowering organizations to achieve sustainability goals through comprehensive assessment and actionable insights.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
