import React, { createContext, useContext, useReducer, ReactNode } from 'react';
import { Assessment } from '@/types/assessment';

interface AssessmentState {
  currentStep: number;
  assessment: Partial<Assessment>;
  isSubmitting: boolean;
  error: string | null;
}

type AssessmentAction =
  | { type: 'SET_STEP'; payload: number }
  | { type: 'SET_ORG_DETAILS'; payload: { orgSize: string; industry: string } }
  | { type: 'SET_RESPONSE'; payload: { questionId: string; value: number } }
  | { type: 'SET_SUBMITTING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'RESET_ASSESSMENT' };

const initialState: AssessmentState = {
  currentStep: 0,
  assessment: {
    sessionId: crypto.randomUUID(),
    responses: {}
  },
  isSubmitting: false,
  error: null
};

function assessmentReducer(state: AssessmentState, action: AssessmentAction): AssessmentState {
  switch (action.type) {
    case 'SET_STEP':
      return { ...state, currentStep: action.payload };
    case 'SET_ORG_DETAILS':
      return {
        ...state,
        assessment: { ...state.assessment, ...action.payload }
      };
    case 'SET_RESPONSE':
      return {
        ...state,
        assessment: {
          ...state.assessment,
          responses: {
            ...state.assessment.responses,
            [action.payload.questionId]: action.payload.value
          }
        }
      };
    case 'SET_SUBMITTING':
      return { ...state, isSubmitting: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    case 'RESET_ASSESSMENT':
      return {
        ...initialState,
        assessment: {
          sessionId: crypto.randomUUID(),
          responses: {}
        }
      };
    default:
      return state;
  }
}

interface AssessmentContextType {
  state: AssessmentState;
  dispatch: React.Dispatch<AssessmentAction>;
  nextStep: () => void;
  previousStep: () => void;
  setOrgDetails: (details: { orgSize: string; industry: string }) => void;
  setResponse: (questionId: string, value: number) => void;
  resetAssessment: () => void;
}

const AssessmentContext = createContext<AssessmentContextType | undefined>(undefined);

export function AssessmentProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(assessmentReducer, initialState);

  const nextStep = () => {
    dispatch({ type: 'SET_STEP', payload: state.currentStep + 1 });
  };

  const previousStep = () => {
    dispatch({ type: 'SET_STEP', payload: Math.max(0, state.currentStep - 1) });
  };

  const setOrgDetails = (details: { orgSize: string; industry: string }) => {
    dispatch({ type: 'SET_ORG_DETAILS', payload: details });
  };

  const setResponse = (questionId: string, value: number) => {
    dispatch({ type: 'SET_RESPONSE', payload: { questionId, value } });
  };

  const resetAssessment = () => {
    dispatch({ type: 'RESET_ASSESSMENT' });
  };

  return (
    <AssessmentContext.Provider value={{
      state,
      dispatch,
      nextStep,
      previousStep,
      setOrgDetails,
      setResponse,
      resetAssessment
    }}>
      {children}
    </AssessmentContext.Provider>
  );
}

export function useAssessment() {
  const context = useContext(AssessmentContext);
  if (context === undefined) {
    throw new Error('useAssessment must be used within an AssessmentProvider');
  }
  return context;
}