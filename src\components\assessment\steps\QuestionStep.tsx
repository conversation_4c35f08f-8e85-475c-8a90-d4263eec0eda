import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { useAssessment } from '@/contexts/AssessmentContext';
import { AssessmentCategory, ANSWER_OPTIONS } from '@/types/assessment';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface QuestionStepProps {
  category: AssessmentCategory;
}

export function QuestionStep({ category }: QuestionStepProps) {
  const { state, previousStep, nextStep, setResponse } = useAssessment();
  const { assessment } = state;

  const progress = ((state.currentStep - 1) / 7) * 100;

  const allQuestionsAnswered = category.questions.every(
    question => assessment.responses?.[question.id] !== undefined
  );

  const handleAnswerChange = (questionId: string, value: string) => {
    setResponse(questionId, parseInt(value));
  };

  return (
    <div className="max-w-3xl mx-auto">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold">{category.name}</h2>
          <span className="text-sm text-muted-foreground">
            Step {state.currentStep - 1} of 7
          </span>
        </div>
        <Progress value={progress} className="mb-4" />
      </div>

      <div className="space-y-6">
        {category.questions.map((question, index) => (
          <Card key={question.id}>
            <CardHeader>
              <CardTitle className="text-lg">
                {index + 1}. {question.text}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <RadioGroup
                value={assessment.responses?.[question.id]?.toString() || ''}
                onValueChange={(value) => handleAnswerChange(question.id, value)}
              >
                {ANSWER_OPTIONS.map((option) => (
                  <div key={option.value} className="flex items-center space-x-2 p-3 rounded-lg hover:bg-accent">
                    <RadioGroupItem value={option.value.toString()} id={`${question.id}-${option.value}`} />
                    <Label 
                      htmlFor={`${question.id}-${option.value}`} 
                      className="flex-1 cursor-pointer"
                    >
                      <div className="font-medium">{option.label}</div>
                      <div className="text-sm text-muted-foreground">{option.description}</div>
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="flex justify-between mt-8">
        <Button variant="outline" onClick={previousStep}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <Button onClick={nextStep} disabled={!allQuestionsAnswered}>
          {state.currentStep === 8 ? 'View Results' : 'Next Category'}
          <ArrowRight className="h-4 w-4 ml-2" />
        </Button>
      </div>
    </div>
  );
}