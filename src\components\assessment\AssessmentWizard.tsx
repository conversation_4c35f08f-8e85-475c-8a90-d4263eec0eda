import React from 'react';
import { useAssessment } from '@/contexts/AssessmentContext';
import { LandingStep } from './steps/LandingStep';
import { OrganizationStep } from './steps/OrganizationStep';
import { QuestionStep } from './steps/QuestionStep';
import { ResultsStep } from './steps/ResultsStep';
import { LeadCaptureStep } from './steps/LeadCaptureStep';
import { ASSESSMENT_CATEGORIES } from '@/types/assessment';

export function AssessmentWizard() {
  const { state } = useAssessment();
  const { currentStep } = state;

  const renderStep = () => {
    if (currentStep === 0) return <LandingStep />;
    if (currentStep === 1) return <OrganizationStep />;
    if (currentStep >= 2 && currentStep <= 8) {
      const categoryIndex = currentStep - 2;
      return <QuestionStep category={ASSESSMENT_CATEGORIES[categoryIndex]} />;
    }
    if (currentStep === 9) return <ResultsStep />;
    if (currentStep === 10) return <LeadCaptureStep />;
    
    return <LandingStep />;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {renderStep()}
    </div>
  );
}