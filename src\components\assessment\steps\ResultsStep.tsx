import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAssessment } from '@/contexts/AssessmentContext';
import { calculateScore, getCategoryScore, ASSESSMENT_CATEGORIES } from '@/types/assessment';
import { supabase } from '@/integrations/supabase/client';
import { Award, TrendingUp, Target, ArrowRight } from 'lucide-react';
import { RadarChart } from '@/components/assessment/RadarChart';
import { useToast } from '@/hooks/use-toast';

export function ResultsStep() {
  const { state, nextStep } = useAssessment();
  const { assessment } = state;
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [assessmentId, setAssessmentId] = useState<string | null>(null);

  const { score, maturityLevel } = calculateScore(assessment.responses || {});

  const categoryScores = ASSESSMENT_CATEGORIES.map(category => ({
    ...category,
    score: getCategoryScore(category.id, assessment.responses || {})
  }));

  const lowestScoring = categoryScores
    .filter(cat => cat.score > 0)
    .sort((a, b) => a.score - b.score)
    .slice(0, 3);

  const getMaturityColor = (level: string) => {
    switch (level) {
      case 'Leading': return 'bg-success text-success-foreground';
      case 'Established': return 'bg-primary text-primary-foreground';
      case 'Developing': return 'bg-warning text-warning-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  useEffect(() => {
    const saveAssessment = async () => {
      if (isSaving || assessmentId) return;
      
      setIsSaving(true);
      try {
        const { data, error } = await supabase
          .from('assessments')
          .insert([{
            session_id: assessment.sessionId,
            org_size: assessment.orgSize!,
            industry: assessment.industry!,
            responses: assessment.responses!,
            score,
            maturity_level: maturityLevel
          }])
          .select()
          .single();

        if (error) throw error;
        setAssessmentId(data.id);
      } catch (error) {
        console.error('Error saving assessment:', error);
        toast({
          title: 'Error',
          description: 'Failed to save assessment results',
          variant: 'destructive'
        });
      } finally {
        setIsSaving(false);
      }
    };

    saveAssessment();
  }, [assessment, score, maturityLevel, isSaving, assessmentId, toast]);

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <div className="text-center">
        <Award className="h-16 w-16 text-primary mx-auto mb-4" />
        <h2 className="text-3xl font-bold mb-2">Your Sustainability Assessment Results</h2>
        <p className="text-muted-foreground">
          Here's your comprehensive Net Zero maturity analysis
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-4xl font-bold text-primary">{score}%</CardTitle>
            <CardDescription>Overall Sustainability Score</CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Badge className={getMaturityColor(maturityLevel)} variant="secondary">
              {maturityLevel} Organization
            </Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Category Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <RadarChart data={categoryScores} />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2" />
            Priority Recommendations
          </CardTitle>
          <CardDescription>
            Focus on these areas to maximize your sustainability impact
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {lowestScoring.map((category, index) => (
              <div key={category.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-semibold">{index + 1}. {category.name}</h4>
                  <Badge variant="outline">{category.score}% complete</Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  {getRecommendation(category.name)}
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Want personalized support?</CardTitle>
            <CardDescription>
              Share your details to receive tailored recommendations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={nextStep} size="lg" className="w-full">
              Get Personalized Support
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

function getRecommendation(categoryName: string): string {
  const recommendations = {
    'Carbon Management': 'Start by establishing a carbon baseline measurement system and set science-based targets aligned with Net Zero commitments.',
    'Energy & Resources': 'Implement energy monitoring systems and explore renewable energy options to reduce your environmental footprint.',
    'Supply Chain & Procurement': 'Develop supplier sustainability criteria and integrate environmental considerations into procurement decisions.',
    'Waste & Circular Economy': 'Establish waste measurement protocols and create recycling programs to move towards circular practices.',
    'Travel & Logistics': 'Implement travel tracking systems and develop policies to reduce business travel emissions.',
    'Governance & Leadership': 'Establish executive sustainability responsibilities and create employee training programs.',
    'Adaptation & Resilience': 'Conduct climate risk assessments and develop adaptation strategies for business continuity.'
  };
  
  return recommendations[categoryName as keyof typeof recommendations] || 'Focus on developing systematic approaches in this area.';
}