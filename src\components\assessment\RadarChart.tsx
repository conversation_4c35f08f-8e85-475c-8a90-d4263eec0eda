import React from 'react';
import { Re<PERSON>onsiveContaine<PERSON>, <PERSON><PERSON>hart as <PERSON><PERSON><PERSON>RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

interface RadarChartProps {
  data: Array<{
    name: string;
    score: number;
  }>;
}

export function RadarChart({ data }: RadarChartProps) {
  const chartData = data.map(item => ({
    category: item.name.split(' ').slice(0, 2).join(' '), // Shorten names for display
    score: item.score
  }));

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <RechartsRadarChart data={chartData}>
          <PolarGrid />
          <PolarAngleAxis 
            dataKey="category" 
            tick={{ fontSize: 10 }}
            className="text-xs"
          />
          <PolarRadiusAxis 
            angle={90} 
            domain={[0, 100]} 
            tick={{ fontSize: 8 }}
            className="text-xs"
          />
          <Radar
            name="Score"
            dataKey="score"
            stroke="hsl(var(--primary))"
            fill="hsl(var(--primary))"
            fillOpacity={0.3}
            strokeWidth={2}
          />
        </RechartsRadarChart>
      </ResponsiveContainer>
    </div>
  );
}