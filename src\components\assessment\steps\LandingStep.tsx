import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAssessment } from '@/contexts/AssessmentContext';
import { Leaf, Target, Award, BarChart } from 'lucide-react';

export function LandingStep() {
  const { nextStep } = useAssessment();

  return (
    <div className="max-w-4xl mx-auto">
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-6">
          <Leaf className="h-12 w-12 text-primary mr-3" />
          <h1 className="text-4xl font-bold text-foreground">Net Zero Assessment</h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover your organization's sustainability maturity level with our comprehensive 
          Net Zero assessment. Get instant results and personalized recommendations.
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6 mb-12">
        <Card>
          <CardHeader className="text-center">
            <Target className="h-8 w-8 text-primary mx-auto mb-2" />
            <CardTitle className="text-lg">Comprehensive Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Evaluate your progress across 7 key sustainability categories including 
              carbon management, energy efficiency, and governance.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <BarChart className="h-8 w-8 text-primary mx-auto mb-2" />
            <CardTitle className="text-lg">Instant Results</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Get your sustainability maturity score immediately with detailed 
              breakdowns and benchmarking against industry standards.
            </CardDescription>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Award className="h-8 w-8 text-primary mx-auto mb-2" />
            <CardTitle className="text-lg">Actionable Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <CardDescription>
              Receive tailored recommendations to accelerate your journey 
              towards Net Zero and improve your sustainability performance.
            </CardDescription>
          </CardContent>
        </Card>
      </div>

      <div className="text-center">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Ready to get started?</CardTitle>
            <CardDescription>
              The assessment takes approximately 5 minutes to complete
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={nextStep} size="lg" className="w-full">
              Start Your Assessment
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}