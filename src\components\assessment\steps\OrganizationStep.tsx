import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAssessment } from '@/contexts/AssessmentContext';
import { ORG_SIZES, INDUSTRIES } from '@/types/assessment';
import { ArrowLeft, ArrowRight, Building2 } from 'lucide-react';

export function OrganizationStep() {
  const { state, previousStep, nextStep, setOrgDetails } = useAssessment();
  const [orgSize, setOrgSize] = useState(state.assessment.orgSize || '');
  const [industry, setIndustry] = useState(state.assessment.industry || '');

  const canContinue = orgSize && industry;

  const handleNext = () => {
    if (canContinue) {
      setOrgDetails({ orgSize, industry });
      nextStep();
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="mb-8 text-center">
        <Building2 className="h-12 w-12 text-primary mx-auto mb-4" />
        <h2 className="text-3xl font-bold mb-2">Tell us about your organization</h2>
        <p className="text-muted-foreground">
          This helps us provide more relevant benchmarking and recommendations
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Organization Details</CardTitle>
          <CardDescription>Step 1 of 8</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Organization Size</label>
            <Select value={orgSize} onValueChange={setOrgSize}>
              <SelectTrigger>
                <SelectValue placeholder="Select your organization size" />
              </SelectTrigger>
              <SelectContent>
                {ORG_SIZES.map((size) => (
                  <SelectItem key={size} value={size}>
                    {size}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Industry</label>
            <Select value={industry} onValueChange={setIndustry}>
              <SelectTrigger>
                <SelectValue placeholder="Select your industry" />
              </SelectTrigger>
              <SelectContent>
                {INDUSTRIES.map((ind) => (
                  <SelectItem key={ind} value={ind}>
                    {ind}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex justify-between pt-6">
            <Button variant="outline" onClick={previousStep}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <Button onClick={handleNext} disabled={!canContinue}>
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}