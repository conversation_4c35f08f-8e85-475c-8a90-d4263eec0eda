import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAssessment } from '@/contexts/AssessmentContext';
import { supabase } from '@/integrations/supabase/client';
import { CheckCircle, Mail, Building, User } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export function LeadCaptureStep() {
  const { resetAssessment } = useAssessment();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    orgName: '',
    email: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Get the latest assessment for this session
      const { data: assessments } = await supabase
        .from('assessments')
        .select('id')
        .order('created_at', { ascending: false })
        .limit(1);

      if (assessments && assessments.length > 0) {
        const { error } = await supabase
          .from('leads')
          .insert([{
            assessment_id: assessments[0].id,
            name: formData.name || null,
            org_name: formData.orgName || null,
            email: formData.email || null
          }]);

        if (error) throw error;
      }

      setIsSubmitted(true);
      toast({
        title: 'Thank you!',
        description: 'Your details have been saved. We\'ll be in touch soon.',
      });
    } catch (error) {
      console.error('Error saving lead:', error);
      toast({
        title: 'Error',
        description: 'Failed to save your details. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSkip = () => {
    resetAssessment();
  };

  if (isSubmitted) {
    return (
      <div className="max-w-2xl mx-auto text-center">
        <Card>
          <CardContent className="pt-8">
            <CheckCircle className="h-16 w-16 text-success mx-auto mb-6" />
            <h2 className="text-2xl font-bold mb-4">Thank You!</h2>
            <p className="text-muted-foreground mb-6">
              We've received your details and will be in touch with personalized recommendations 
              to help accelerate your sustainability journey.
            </p>
            <Button onClick={resetAssessment} size="lg">
              Take Another Assessment
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <Mail className="h-12 w-12 text-primary mx-auto mb-4" />
        <h2 className="text-3xl font-bold mb-2">Get Personalized Support</h2>
        <p className="text-muted-foreground">
          Share your details to receive tailored recommendations and support for your Net Zero journey
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>
            All fields are optional. We respect your privacy and will only use this information 
            to provide you with relevant sustainability insights.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                Your Name
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter your full name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="orgName" className="flex items-center">
                <Building className="h-4 w-4 mr-2" />
                Organization Name
              </Label>
              <Input
                id="orgName"
                value={formData.orgName}
                onChange={(e) => handleInputChange('orgName', e.target.value)}
                placeholder="Enter your organization name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center">
                <Mail className="h-4 w-4 mr-2" />
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="Enter your email address"
              />
            </div>

            <div className="flex gap-4 pt-6">
              <Button type="button" variant="outline" onClick={handleSkip} className="flex-1">
                Skip for Now
              </Button>
              <Button type="submit" disabled={isSubmitting} className="flex-1">
                {isSubmitting ? 'Saving...' : 'Get Support'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}